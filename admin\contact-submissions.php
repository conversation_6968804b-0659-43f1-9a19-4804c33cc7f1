<?php
/**
 * Contact Submissions Admin Panel
 * DigitalAce Admin Panel
 */

// Include database configuration
require_once '../config/database.php';

// Initialize handler
$handler = new ContactFormHandler();

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $submissionId = $_POST['submission_id'] ?? 0;
    $newStatus = $_POST['status'] ?? '';
    
    if ($handler->updateStatus($submissionId, $newStatus)) {
        $successMessage = "Status updated successfully!";
    } else {
        $errorMessage = "Failed to update status.";
    }
}

// Get submissions
$page = $_GET['page'] ?? 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$submissions = $handler->getAllSubmissions($limit, $offset);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Submissions - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/theme-constants.css">
    <link rel="stylesheet" href="../assets/css/index.css">
    <link rel="stylesheet" href="../assets/css/modal-system.css">
    <link rel="stylesheet" href="../assets/css/notification-system.css">
    <link rel="stylesheet" href="../assets/css/responsive-enhancements.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-8);
            background: var(--card-bg);
            min-height: 100vh;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-color);
        }

        .admin-title {
            font-size: var(--font-2xl);
            font-weight: var(--font-bold);
            color: var(--text-color);
        }

        .submissions-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .submissions-table th,
        .submissions-table td {
            padding: var(--space-4);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .submissions-table th {
            background: var(--hover-bg);
            font-weight: var(--font-semibold);
            color: var(--text-color);
        }

        .submissions-table td {
            color: var(--text-secondary);
        }

        .status-badge {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: var(--font-sm);
            font-weight: var(--font-medium);
            text-transform: uppercase;
        }

        .status-new {
            background: rgba(var(--primary-rgb), 0.1);
            color: var(--primary);
        }

        .status-read {
            background: rgba(var(--warning-rgb), 0.1);
            color: var(--warning);
        }

        .status-responded {
            background: rgba(var(--info-rgb), 0.1);
            color: var(--info);
        }

        .status-closed {
            background: rgba(var(--success-rgb), 0.1);
            color: var(--success);
        }

        .action-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: var(--font-sm);
            margin-right: var(--space-2);
            transition: var(--transition);
        }

        .action-btn:hover {
            background: var(--primary-dark);
        }

        .action-btn.secondary {
            background: var(--secondary);
        }

        .action-btn.secondary:hover {
            background: var(--secondary-dark);
        }

        .message-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .alert {
            padding: var(--space-4);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-4);
        }

        .alert-success {
            background: rgba(var(--success-rgb), 0.1);
            border: 1px solid var(--success);
            color: var(--success);
        }

        .alert-error {
            background: rgba(var(--danger-rgb), 0.1);
            border: 1px solid var(--danger);
            color: var(--danger);
        }

        .status-select {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: var(--space-2);
            border-radius: var(--radius-md);
            font-size: var(--font-sm);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1 class="admin-title">
                <i class="fas fa-envelope"></i>
                Contact Submissions
            </h1>
            <a href="../popup.php" class="action-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Form
            </a>
        </div>

        <?php if (isset($successMessage)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($successMessage); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($errorMessage)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($errorMessage); ?>
            </div>
        <?php endif; ?>

        <?php if (empty($submissions)): ?>
            <div class="alert alert-info" style="background: rgba(var(--info-rgb), 0.1); border: 1px solid var(--info); color: var(--info);">
                <i class="fas fa-info-circle"></i>
                No submissions found. The contact form hasn't been submitted yet.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="submissions-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Subject</th>
                            <th>Message</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($submissions as $submission): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($submission['id']); ?></td>
                                <td><?php echo htmlspecialchars($submission['full_name']); ?></td>
                                <td>
                                    <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>" 
                                       style="color: var(--primary); text-decoration: none;">
                                        <?php echo htmlspecialchars($submission['email']); ?>
                                    </a>
                                </td>
                                <td><?php echo ucfirst(htmlspecialchars($submission['subject'])); ?></td>
                                <td>
                                    <div class="message-preview" title="<?php echo htmlspecialchars($submission['message']); ?>">
                                        <?php echo htmlspecialchars($submission['message']); ?>
                                    </div>
                                </td>
                                <td><?php echo date('M j, Y g:i A', strtotime($submission['submission_date'])); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo htmlspecialchars($submission['status']); ?>">
                                        <?php echo ucfirst(htmlspecialchars($submission['status'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn" onclick="viewSubmission(<?php echo $submission['id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                        View
                                    </button>
                                    <form style="display: inline;" method="POST" action="">
                                        <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                        <select name="status" class="status-select" onchange="this.form.submit()">
                                            <option value="">Change Status</option>
                                            <option value="new" <?php echo $submission['status'] === 'new' ? 'selected' : ''; ?>>New</option>
                                            <option value="read" <?php echo $submission['status'] === 'read' ? 'selected' : ''; ?>>Read</option>
                                            <option value="responded" <?php echo $submission['status'] === 'responded' ? 'selected' : ''; ?>>Responded</option>
                                            <option value="closed" <?php echo $submission['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                        </select>
                                        <input type="hidden" name="update_status" value="1">
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- View Submission Modal -->
    <div class="modal-backdrop" id="viewModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-envelope-open"></i>
                    Submission Details
                </h3>
                <button class="modal-close" data-modal="viewModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="viewModal">
                    <i class="fas fa-times"></i>
                    Close
                </button>
            </div>
        </div>
    </div>

    <script src="../assets/js/modal-system.js"></script>
    <script>
        function viewSubmission(id) {
            // Find the submission data from the table
            const rows = document.querySelectorAll('.submissions-table tbody tr');
            let submissionData = null;
            
            rows.forEach(row => {
                if (row.cells[0].textContent == id) {
                    submissionData = {
                        id: row.cells[0].textContent,
                        name: row.cells[1].textContent,
                        email: row.cells[2].textContent.trim(),
                        subject: row.cells[3].textContent,
                        message: row.cells[4].querySelector('.message-preview').title,
                        date: row.cells[5].textContent,
                        status: row.cells[6].textContent.trim()
                    };
                }
            });
            
            if (submissionData) {
                const modalContent = document.getElementById('modalContent');
                modalContent.innerHTML = `
                    <div style="display: grid; gap: var(--space-4);">
                        <div>
                            <strong>Submission ID:</strong> ${submissionData.id}
                        </div>
                        <div>
                            <strong>Full Name:</strong> ${submissionData.name}
                        </div>
                        <div>
                            <strong>Email:</strong> 
                            <a href="mailto:${submissionData.email}" style="color: var(--primary);">
                                ${submissionData.email}
                            </a>
                        </div>
                        <div>
                            <strong>Subject:</strong> ${submissionData.subject}
                        </div>
                        <div>
                            <strong>Submission Date:</strong> ${submissionData.date}
                        </div>
                        <div>
                            <strong>Status:</strong> 
                            <span class="status-badge status-${submissionData.status.toLowerCase()}">
                                ${submissionData.status}
                            </span>
                        </div>
                        <div>
                            <strong>Message:</strong>
                            <div style="background: var(--hover-bg); padding: var(--space-4); border-radius: var(--radius-md); margin-top: var(--space-2); white-space: pre-wrap;">
                                ${submissionData.message}
                            </div>
                        </div>
                    </div>
                `;
                
                window.modalSystem.openModal('viewModal');
            }
        }

        // Initialize modal system
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-refresh page every 30 seconds to show new submissions
            setInterval(() => {
                // Only refresh if no modal is open
                if (!document.querySelector('.modal-backdrop.active')) {
                    location.reload();
                }
            }, 30000);
        });
    </script>
</body>
</html>
