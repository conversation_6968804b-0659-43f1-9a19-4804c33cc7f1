<?php
/**
 * Database Setup Script
 * DigitalAce Admin Panel
 * 
 * This script creates the database and tables needed for the contact form
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'digitalace_admin';

try {
    // Connect to MySQL server (without specifying database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Database Setup - DigitalAce Admin</h2>";
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5; border-radius: 8px;'>";
    
    // Create database if it doesn't exist
    $sql = "CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ Database '$database' created successfully (or already exists)</p>";
    
    // Select the database
    $pdo->exec("USE `$database`");
    
    // Create contact_submissions table
    $sql = "CREATE TABLE IF NOT EXISTS `contact_submissions` (
        `id` INT AUTO_INCREMENT PRIMARY KEY,
        `full_name` VARCHAR(255) NOT NULL,
        `email` VARCHAR(255) NOT NULL,
        `phone` VARCHAR(20) DEFAULT NULL,
        `subject` ENUM('general', 'support', 'billing', 'feedback') NOT NULL,
        `message` TEXT NOT NULL,
        `newsletter_subscription` BOOLEAN DEFAULT FALSE,
        `submission_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `ip_address` VARCHAR(45) DEFAULT NULL,
        `user_agent` TEXT DEFAULT NULL,
        `status` ENUM('new', 'read', 'responded', 'closed') DEFAULT 'new',
        INDEX `idx_email` (`email`),
        INDEX `idx_subject` (`subject`),
        INDEX `idx_status` (`status`),
        INDEX `idx_submission_date` (`submission_date`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ Table 'contact_submissions' created successfully (or already exists)</p>";
    
    // Insert sample data for testing (optional)
    $sampleData = [
        [
            'full_name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'subject' => 'general',
            'message' => 'This is a sample message to test the contact form functionality. Everything looks great!',
            'newsletter_subscription' => 1,
            'ip_address' => '***********',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'status' => 'new'
        ],
        [
            'full_name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'subject' => 'support',
            'message' => 'I need help with my account settings. Could you please assist me with updating my profile information?',
            'newsletter_subscription' => 0,
            'ip_address' => '***********',
            'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'status' => 'read'
        ],
        [
            'full_name' => 'Mike Johnson',
            'email' => '<EMAIL>',
            'phone' => '',
            'subject' => 'billing',
            'message' => 'I have a question about my recent invoice. The charges seem incorrect and I would like clarification.',
            'newsletter_subscription' => 1,
            'ip_address' => '***********',
            'user_agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'status' => 'responded'
        ]
    ];
    
    // Check if sample data already exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM contact_submissions");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $insertSql = "INSERT INTO contact_submissions 
                      (full_name, email, phone, subject, message, newsletter_subscription, ip_address, user_agent, status) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($insertSql);
        
        foreach ($sampleData as $data) {
            $stmt->execute([
                $data['full_name'],
                $data['email'],
                $data['phone'],
                $data['subject'],
                $data['message'],
                $data['newsletter_subscription'],
                $data['ip_address'],
                $data['user_agent'],
                $data['status']
            ]);
        }
        
        echo "<p style='color: blue;'>✓ Sample data inserted successfully (3 test records)</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Sample data not inserted - table already contains $count records</p>";
    }
    
    // Display table structure
    echo "<h3>Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE contact_submissions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #ddd;'>";
    echo "<th style='border: 1px solid #ccc; padding: 8px;'>Field</th>";
    echo "<th style='border: 1px solid #ccc; padding: 8px;'>Type</th>";
    echo "<th style='border: 1px solid #ccc; padding: 8px;'>Null</th>";
    echo "<th style='border: 1px solid #ccc; padding: 8px;'>Key</th>";
    echo "<th style='border: 1px solid #ccc; padding: 8px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ccc; padding: 8px;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='border: 1px solid #ccc; padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='border: 1px solid #ccc; padding: 8px;'>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td style='border: 1px solid #ccc; padding: 8px;'>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td style='border: 1px solid #ccc; padding: 8px;'>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Setup Complete!</h3>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>✓ Database and table are ready</li>";
    echo "<li>✓ You can now use the contact form at <a href='../popup.php' target='_blank'>popup.php</a></li>";
    echo "<li>✓ View submissions in the admin panel at <a href='../admin/contact-submissions.php' target='_blank'>admin/contact-submissions.php</a></li>";
    echo "<li>✓ Update database credentials in <code>config/database.php</code> if needed</li>";
    echo "</ul>";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #2d5a2d; margin: 0 0 10px 0;'>Configuration Notes:</h4>";
    echo "<p style='margin: 5px 0;'><strong>Database:</strong> $database</p>";
    echo "<p style='margin: 5px 0;'><strong>Host:</strong> $host</p>";
    echo "<p style='margin: 5px 0;'><strong>Table:</strong> contact_submissions</p>";
    echo "<p style='margin: 5px 0;'><strong>Records:</strong> " . ($count + count($sampleData)) . " total</p>";
    echo "</div>";
    
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #ffe6e6; border-radius: 8px; color: #d00;'>";
    echo "<h2>Database Setup Error</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Possible Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure MySQL/MariaDB is running</li>";
    echo "<li>Check database credentials in this file</li>";
    echo "<li>Ensure the database user has CREATE privileges</li>";
    echo "<li>Verify the database server is accessible</li>";
    echo "</ul>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - DigitalAce Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DigitalAce Admin</h1>
            <p>Database Setup Complete</p>
        </div>
        
        <div class="content">
            <h3>Quick Links:</h3>
            <a href="../popup.php" class="btn">📝 Contact Form</a>
            <a href="../admin/contact-submissions.php" class="btn">📊 Admin Panel</a>
            <a href="../config/database.php" class="btn">⚙️ Database Config</a>
            
            <h3>File Structure:</h3>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
project/
├── config/
│   └── database.php          # Database configuration & classes
├── admin/
│   └── contact-submissions.php # Admin panel to view submissions
├── setup/
│   └── database-setup.php    # This setup script
├── popup.php                 # Main contact form (PHP version)
└── popup.html               # Original HTML version (for reference)
            </pre>
        </div>
        
        <div class="footer">
            <p>Setup completed successfully! Your contact form is ready to use.</p>
        </div>
    </div>
</body>
</html>
