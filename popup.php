<?php
/**
 * Popup Contact Form - PHP Version
 * DigitalAce Admin Panel
 */

// Include database configuration
require_once 'config/database.php';

// Initialize variables
$formData = [];
$errors = [];
$success = false;
$message = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit'])) {
    $handler = new ContactFormHandler();
    
    // Get form data
    $formData = [
        'fullName' => $_POST['fullName'] ?? '',
        'email' => $_POST['email'] ?? '',
        'phone' => $_POST['phone'] ?? '',
        'subject' => $_POST['subject'] ?? '',
        'message' => $_POST['message'] ?? '',
        'newsletter' => $_POST['newsletter'] ?? ''
    ];
    
    // Validate data
    $errors = $handler->validateData($formData);
    
    if (empty($errors)) {
        // Sanitize data
        $cleanData = $handler->sanitizeData($formData);
        
        // Save to database
        $result = $handler->saveSubmission($cleanData);
        
        if ($result['success']) {
            $success = true;
            $message = $result['message'];
            // Clear form data on success
            $formData = [];
        } else {
            $message = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form - DigitalAce</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
    <style>
        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-family-primary);
            margin: 0;
            padding: var(--space-4);
        }

        .demo-container {
            text-align: center;
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: var(--space-16);
            box-shadow: var(--card-shadow-hover);
            max-width: 400px;
            width: 100%;
        }

        .demo-title {
            font-size: var(--font-3xl);
            font-weight: var(--font-bold);
            color: var(--text-color);
            margin-bottom: var(--space-4);
            text-shadow: var(--text-shadow);
        }

        .demo-description {
            color: var(--text-secondary);
            margin-bottom: var(--space-8);
            line-height: var(--leading-relaxed);
        }

        .click-button {
            background: var(--primary);
            color: white;
            border: none;
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-lg);
            font-size: var(--font-lg);
            font-weight: var(--font-semibold);
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--button-shadow);
            display: inline-flex;
            align-items: center;
            gap: var(--space-3);
        }

        .click-button:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: var(--button-shadow-hover);
        }

        .click-button:active {
            transform: translateY(-1px);
        }

        .theme-toggle {
            position: absolute;
            top: var(--space-4);
            right: var(--space-4);
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: var(--font-lg);
        }

        .theme-toggle:hover {
            background: var(--hover-bg);
            transform: rotate(180deg);
        }

        .form-error {
            color: var(--danger);
            font-size: var(--font-sm);
            margin-top: var(--space-1);
            display: block;
        }

        .form-input.error {
            border-color: var(--danger);
            background-color: rgba(var(--danger-rgb), 0.1);
        }

        .success-message {
            background: rgba(var(--success-rgb), 0.1);
            border: 1px solid var(--success);
            color: var(--success);
            padding: var(--space-4);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-4);
            text-align: center;
        }

        .error-message {
            background: rgba(var(--danger-rgb), 0.1);
            border: 1px solid var(--danger);
            color: var(--danger);
            padding: var(--space-4);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-4);
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <button class="theme-toggle" id="themeToggle">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Main Demo Container -->
    <div class="demo-container">
        <h1 class="demo-title">
            <i class="fas fa-rocket"></i>
            Contact Form
        </h1>
        <p class="demo-description">
            Fill out the form below to get in touch with us. We'll respond to your message as soon as possible.
        </p>
        <button class="click-button" id="openPopupBtn">
            <i class="fas fa-envelope"></i>
            Contact Us
        </button>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Contact Form Modal -->
    <div class="modal-backdrop" id="demoModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-edit"></i>
                    Contact Form
                </h3>
                <button class="modal-close" data-modal="demoModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <?php if ($success): ?>
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php elseif (!empty($message)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <form class="modal-form" id="demoForm" method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                    <div class="form-group">
                        <label class="form-label">Full Name *</label>
                        <input 
                            name="fullName" 
                            type="text" 
                            class="form-input <?php echo isset($errors['fullName']) ? 'error' : ''; ?>" 
                            placeholder="Enter your full name" 
                            value="<?php echo htmlspecialchars($formData['fullName'] ?? ''); ?>"
                            required
                        >
                        <?php if (isset($errors['fullName'])): ?>
                            <span class="form-error"><?php echo htmlspecialchars($errors['fullName']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email Address *</label>
                        <input 
                            name="email" 
                            type="email" 
                            class="form-input <?php echo isset($errors['email']) ? 'error' : ''; ?>" 
                            placeholder="Enter your email" 
                            value="<?php echo htmlspecialchars($formData['email'] ?? ''); ?>"
                            required
                        >
                        <?php if (isset($errors['email'])): ?>
                            <span class="form-error"><?php echo htmlspecialchars($errors['email']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Phone Number</label>
                        <input 
                            name="phone" 
                            type="tel" 
                            class="form-input <?php echo isset($errors['phone']) ? 'error' : ''; ?>" 
                            placeholder="Enter your phone number"
                            value="<?php echo htmlspecialchars($formData['phone'] ?? ''); ?>"
                        >
                        <?php if (isset($errors['phone'])): ?>
                            <span class="form-error"><?php echo htmlspecialchars($errors['phone']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Subject *</label>
                        <select class="form-input form-select <?php echo isset($errors['subject']) ? 'error' : ''; ?>" name="subject" required>
                            <option value="">Select a subject</option>
                            <option value="general" <?php echo ($formData['subject'] ?? '') === 'general' ? 'selected' : ''; ?>>General Inquiry</option>
                            <option value="support" <?php echo ($formData['subject'] ?? '') === 'support' ? 'selected' : ''; ?>>Technical Support</option>
                            <option value="billing" <?php echo ($formData['subject'] ?? '') === 'billing' ? 'selected' : ''; ?>>Billing Question</option>
                            <option value="feedback" <?php echo ($formData['subject'] ?? '') === 'feedback' ? 'selected' : ''; ?>>Feedback</option>
                        </select>
                        <?php if (isset($errors['subject'])): ?>
                            <span class="form-error"><?php echo htmlspecialchars($errors['subject']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Message *</label>
                        <textarea 
                            name="message" 
                            class="form-input form-textarea <?php echo isset($errors['message']) ? 'error' : ''; ?>" 
                            placeholder="Enter your message here..." 
                            required 
                            style="min-height: 100px;"
                        ><?php echo htmlspecialchars($formData['message'] ?? ''); ?></textarea>
                        <?php if (isset($errors['message'])): ?>
                            <span class="form-error"><?php echo htmlspecialchars($errors['message']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" style="display: flex; align-items: center; gap: 8px;">
                            <input 
                                type="checkbox" 
                                name="newsletter" 
                                style="margin: 0;"
                                <?php echo isset($formData['newsletter']) && $formData['newsletter'] ? 'checked' : ''; ?>
                            >
                            Subscribe to our newsletter
                        </label>
                    </div>
                    
                    <button name="submit" type="submit" class="modal-btn modal-btn-primary" id="submitFormBtn">
                        <i class="fas fa-paper-plane"></i>
                        Submit
                    </button>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="demoModal">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Include JavaScript Files -->
    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    
    <script>
        // Initialize the popup demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Theme toggle functionality
            const themeToggle = document.getElementById('themeToggle');
            const currentTheme = localStorage.getItem('theme') || 'dark';
            
            // Set initial theme
            document.documentElement.setAttribute('data-theme', currentTheme);
            updateThemeIcon(currentTheme);
            
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            });
            
            function updateThemeIcon(theme) {
                const icon = themeToggle.querySelector('i');
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            // Open popup button
            const openPopupBtn = document.getElementById('openPopupBtn');
            openPopupBtn.addEventListener('click', () => {
                window.modalSystem.openModal('demoModal');
            });

            // Initialize notification system
            window.notificationSystem = new NotificationSystem();
            
            <?php if ($success): ?>
            // Show success notification
            setTimeout(() => {
                window.notificationSystem.success(
                    'Message Sent!', 
                    'Thank you for contacting us. We will get back to you soon.',
                    { duration: 5000 }
                );
                // Auto-close modal after success
                setTimeout(() => {
                    window.modalSystem.closeModal('demoModal');
                }, 1000);
            }, 500);
            <?php elseif (!empty($errors)): ?>
            // Show error notification and open modal
            setTimeout(() => {
                window.notificationSystem.error(
                    'Form Error', 
                    'Please check the form for errors and try again.',
                    { duration: 5000 }
                );
                window.modalSystem.openModal('demoModal');
            }, 500);
            <?php else: ?>
            // Show welcome notification
            setTimeout(() => {
                window.notificationSystem.info(
                    'Welcome!', 
                    'Click the "Contact Us" button to get in touch with us.',
                    { duration: 4000 }
                );
            }, 1000);
            <?php endif; ?>
        });
    </script>
</body>
</html>
