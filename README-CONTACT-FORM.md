# DigitalAce Contact Form - PHP Implementation

This is a complete PHP implementation of the contact form with database integration, based on your original `popup.html` file.

## 📁 File Structure

```
project/
├── config/
│   └── database.php              # Database configuration & handler classes
├── admin/
│   └── contact-submissions.php   # Admin panel to view/manage submissions
├── setup/
│   └── database-setup.php        # Database setup script
├── popup.php                     # Main contact form (PHP version)
├── popup.html                    # Original HTML version (for reference)
└── README-CONTACT-FORM.md        # This file
```

## 🚀 Quick Start

### 1. Database Setup
1. Make sure MySQL/MariaDB is running
2. Run the setup script: `http://your-domain/setup/database-setup.php`
3. This will create the database `digitalace_admin` and the `contact_submissions` table

### 2. Configuration
Update database credentials in `config/database.php` if needed:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'digitalace_admin');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 3. Usage
- **Contact Form**: `http://your-domain/popup.php`
- **Admin Panel**: `http://your-domain/admin/contact-submissions.php`

## ✨ Features

### Contact Form (`popup.php`)
- ✅ **Responsive Design**: Maintains your original theme and styling
- ✅ **Form Validation**: Client-side and server-side validation
- ✅ **Error Handling**: Shows validation errors inline
- ✅ **Success Feedback**: Confirmation messages and notifications
- ✅ **Data Sanitization**: Prevents XSS and SQL injection
- ✅ **Newsletter Subscription**: Optional checkbox
- ✅ **Theme Toggle**: Dark/light mode support
- ✅ **Modal System**: Uses your existing modal JavaScript

### Admin Panel (`admin/contact-submissions.php`)
- ✅ **View All Submissions**: Paginated list of all form submissions
- ✅ **Status Management**: Update submission status (New, Read, Responded, Closed)
- ✅ **Detailed View**: Modal popup with full submission details
- ✅ **Email Links**: Click-to-email functionality
- ✅ **Auto Refresh**: Updates every 30 seconds
- ✅ **Responsive Design**: Works on all devices

### Database Features
- ✅ **Secure Storage**: PDO with prepared statements
- ✅ **Data Tracking**: IP address, user agent, timestamps
- ✅ **Indexed Fields**: Optimized for performance
- ✅ **Status Workflow**: Track submission lifecycle
- ✅ **UTF-8 Support**: Full Unicode character support

## 📊 Database Schema

### `contact_submissions` Table
| Field | Type | Description |
|-------|------|-------------|
| `id` | INT AUTO_INCREMENT | Primary key |
| `full_name` | VARCHAR(255) | Submitter's full name |
| `email` | VARCHAR(255) | Email address |
| `phone` | VARCHAR(20) | Phone number (optional) |
| `subject` | ENUM | general, support, billing, feedback |
| `message` | TEXT | Message content |
| `newsletter_subscription` | BOOLEAN | Newsletter opt-in |
| `submission_date` | TIMESTAMP | When submitted |
| `ip_address` | VARCHAR(45) | Submitter's IP |
| `user_agent` | TEXT | Browser information |
| `status` | ENUM | new, read, responded, closed |

## 🔧 Customization

### Adding New Subject Options
1. Update the ENUM in the database:
```sql
ALTER TABLE contact_submissions 
MODIFY COLUMN subject ENUM('general', 'support', 'billing', 'feedback', 'new_option');
```

2. Update the form options in `popup.php`:
```html
<option value="new_option">New Option</option>
```

3. Update validation in `config/database.php`:
```php
$validSubjects = ['general', 'support', 'billing', 'feedback', 'new_option'];
```

### Styling Customization
The form uses your existing CSS variables from `theme-constants.css`. You can customize:
- Colors: Update CSS custom properties
- Layout: Modify the modal and form styles
- Animations: Adjust transitions and effects

### Email Notifications (Optional Enhancement)
To send email notifications when forms are submitted, add this to the `saveSubmission` method:

```php
// After successful database save
if ($result) {
    // Send notification email
    $to = '<EMAIL>';
    $subject = 'New Contact Form Submission';
    $message = "New submission from: " . $data['fullName'];
    $headers = 'From: <EMAIL>';
    
    mail($to, $subject, $message, $headers);
    
    return [
        'success' => true,
        'message' => 'Your message has been submitted successfully!',
        'submission_id' => $this->conn->lastInsertId()
    ];
}
```

## 🛡️ Security Features

- **SQL Injection Protection**: PDO prepared statements
- **XSS Prevention**: HTML entity encoding
- **CSRF Protection**: Form token validation (can be added)
- **Input Validation**: Server-side validation for all fields
- **Data Sanitization**: Clean all user inputs
- **Error Logging**: Database errors logged securely

## 📱 Mobile Responsive

The form is fully responsive and works on:
- ✅ Desktop computers
- ✅ Tablets
- ✅ Mobile phones
- ✅ All modern browsers

## 🔍 Testing

### Test the Form
1. Go to `popup.php`
2. Click "Contact Us"
3. Fill out and submit the form
4. Check the admin panel for the submission

### Test Validation
- Try submitting empty fields
- Enter invalid email addresses
- Test with very short messages
- Verify error messages appear

### Test Admin Panel
- View submissions
- Change status values
- Use the detailed view modal
- Test email links

## 🚨 Troubleshooting

### Database Connection Issues
- Check MySQL is running
- Verify credentials in `config/database.php`
- Ensure database user has proper permissions

### Form Not Submitting
- Check PHP error logs
- Verify file permissions
- Test database connection

### Styling Issues
- Ensure CSS files are loading
- Check browser console for errors
- Verify file paths are correct

## 📈 Future Enhancements

Potential improvements you could add:
- Email notifications for new submissions
- File upload support
- CAPTCHA integration
- Export submissions to CSV
- Advanced filtering and search
- Response templates
- Automated responses
- Integration with CRM systems

## 🎯 Key Benefits

1. **Maintains Your Design**: Uses your existing theme and styling
2. **Secure & Robust**: Professional-grade security measures
3. **Easy to Manage**: Simple admin interface
4. **Scalable**: Can handle high volumes of submissions
5. **Customizable**: Easy to modify and extend
6. **Mobile-First**: Responsive design for all devices

Your contact form is now ready for production use! 🎉
