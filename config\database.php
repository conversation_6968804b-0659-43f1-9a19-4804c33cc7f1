<?php
/**
 * Database Configuration File
 * DigitalAce Admin Panel
 */

// Database configuration constants
define('DB_HOST', 'localhost');
define('DB_NAME', 'digitalace_admin');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

/**
 * Database Connection Class
 */
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;

    /**
     * Get database connection
     */
    public function getConnection() {
        $this->pdo = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->pdo;
    }

    /**
     * Create the contact_submissions table if it doesn't exist
     */
    public function createTable() {
        $sql = "CREATE TABLE IF NOT EXISTS contact_submissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            subject ENUM('general', 'support', 'billing', 'feedback') NOT NULL,
            message TEXT NOT NULL,
            newsletter_subscription BOOLEAN DEFAULT FALSE,
            submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            status ENUM('new', 'read', 'responded', 'closed') DEFAULT 'new',
            INDEX idx_email (email),
            INDEX idx_subject (subject),
            INDEX idx_status (status),
            INDEX idx_submission_date (submission_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $conn = $this->getConnection();
            $conn->exec($sql);
            return true;
        } catch(PDOException $e) {
            error_log("Table creation error: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * Contact Form Handler Class
 */
class ContactFormHandler {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
        
        // Create table if it doesn't exist
        $this->db->createTable();
    }

    /**
     * Validate form data
     */
    public function validateData($data) {
        $errors = [];

        // Validate full name
        if (empty($data['fullName']) || strlen(trim($data['fullName'])) < 2) {
            $errors['fullName'] = 'Full name is required and must be at least 2 characters long.';
        }

        // Validate email
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'A valid email address is required.';
        }

        // Validate phone (optional but if provided, should be valid)
        if (!empty($data['phone']) && !preg_match('/^[\+]?[0-9\s\-\(\)]{10,}$/', $data['phone'])) {
            $errors['phone'] = 'Please enter a valid phone number.';
        }

        // Validate subject
        $validSubjects = ['general', 'support', 'billing', 'feedback'];
        if (empty($data['subject']) || !in_array($data['subject'], $validSubjects)) {
            $errors['subject'] = 'Please select a valid subject.';
        }

        // Validate message
        if (empty($data['message']) || strlen(trim($data['message'])) < 10) {
            $errors['message'] = 'Message is required and must be at least 10 characters long.';
        }

        return $errors;
    }

    /**
     * Sanitize form data
     */
    public function sanitizeData($data) {
        return [
            'fullName' => htmlspecialchars(trim($data['fullName']), ENT_QUOTES, 'UTF-8'),
            'email' => filter_var(trim($data['email']), FILTER_SANITIZE_EMAIL),
            'phone' => htmlspecialchars(trim($data['phone']), ENT_QUOTES, 'UTF-8'),
            'subject' => htmlspecialchars(trim($data['subject']), ENT_QUOTES, 'UTF-8'),
            'message' => htmlspecialchars(trim($data['message']), ENT_QUOTES, 'UTF-8'),
            'newsletter' => isset($data['newsletter']) ? 1 : 0
        ];
    }

    /**
     * Save form data to database
     */
    public function saveSubmission($data) {
        $sql = "INSERT INTO contact_submissions 
                (full_name, email, phone, subject, message, newsletter_subscription, ip_address, user_agent) 
                VALUES (:full_name, :email, :phone, :subject, :message, :newsletter, :ip_address, :user_agent)";

        try {
            $stmt = $this->conn->prepare($sql);
            
            $result = $stmt->execute([
                ':full_name' => $data['fullName'],
                ':email' => $data['email'],
                ':phone' => $data['phone'],
                ':subject' => $data['subject'],
                ':message' => $data['message'],
                ':newsletter' => $data['newsletter'],
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            if ($result) {
                return [
                    'success' => true,
                    'message' => 'Your message has been submitted successfully!',
                    'submission_id' => $this->conn->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to save your submission. Please try again.'
                ];
            }
        } catch(PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'A database error occurred. Please try again later.'
            ];
        }
    }

    /**
     * Get all submissions (for admin panel)
     */
    public function getAllSubmissions($limit = 50, $offset = 0) {
        $sql = "SELECT * FROM contact_submissions 
                ORDER BY submission_date DESC 
                LIMIT :limit OFFSET :offset";

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update submission status
     */
    public function updateStatus($id, $status) {
        $validStatuses = ['new', 'read', 'responded', 'closed'];
        if (!in_array($status, $validStatuses)) {
            return false;
        }

        $sql = "UPDATE contact_submissions SET status = :status WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([
                ':status' => $status,
                ':id' => $id
            ]);
        } catch(PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            return false;
        }
    }
}
?>
