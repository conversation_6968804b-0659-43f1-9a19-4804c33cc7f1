# DigitalAce Admin Panel

A modern, responsive admin dashboard with consistent theming, popup modals, and notification systems.

## Features

### 🎨 **Consistent Theme System**
- Centralized theme constants in `assets/css/theme-constants.css`
- Dark/Light theme toggle with localStorage persistence
- CSS custom properties for easy customization
- Consistent color palette and typography across all components

### 🔔 **Advanced Notification System**
- Toast notifications with 4 types: Success, Error, Warning, Info
- Auto-dismiss functionality with progress bars
- Action buttons within notifications
- Responsive design for all screen sizes
- Queue management (max 5 notifications)

### 📱 **Modal & Popup System**
- Reusable modal components with backdrop blur
- Form modals for creating projects and tickets
- Confirmation modals for delete actions
- Responsive design with mobile-first approach
- Keyboard navigation (ESC to close)
- Focus management for accessibility

### 📱 **Fully Responsive Design**
- Mobile-first responsive design
- Collapsible sidebar navigation
- Touch-friendly interface elements
- Responsive tables that convert to cards on mobile
- Optimized for tablets and mobile devices

### 🛠 **Admin Features**
- Dashboard with statistics cards
- Project management with CRUD operations
- Settings page with toggle switches
- User profile dropdown
- Data tables with action buttons

## File Structure

```
├── index.html              # Main dashboard page
├── projects.html           # Projects management page
├── settings.html           # System settings page
├── README.md              # This file
└── assets/
    ├── css/
    │   ├── theme-constants.css        # Theme variables and constants
    │   ├── index.css                  # Main dashboard styles
    │   ├── modal-system.css           # Modal and popup styles
    │   ├── notification-system.css    # Notification toast styles
    │   └── responsive-enhancements.css # Additional responsive styles
    └── js/
        ├── index.js                   # Main dashboard functionality
        ├── modal-system.js            # Modal system JavaScript
        └── notification-system.js     # Notification system JavaScript
```

## Getting Started

1. **Open the admin panel**: Open `index.html` in your web browser
2. **Navigate between pages**: Use the sidebar navigation to switch between Dashboard, Projects, and Settings
3. **Test notifications**: Click the notification icons in the top navigation bar
4. **Try modals**: Click "New Project" or "New Ticket" buttons to open modal forms
5. **Toggle theme**: Use the theme toggle button in the top navigation

## Usage Examples

### Creating Notifications

```javascript
// Success notification
window.notificationSystem.success('Success!', 'Operation completed successfully.');

// Error notification with actions
window.notificationSystem.error('Error', 'Something went wrong.', {
    persistent: true,
    actions: [
        { text: 'Retry', type: 'primary', action: 'retry' },
        { text: 'Report', type: 'secondary', action: 'report' }
    ]
});
```

### Opening Modals

```javascript
// Open a modal
window.modalSystem.openModal('newProjectModal');

// Show confirmation dialog
window.modalSystem.showConfirmation(
    'Delete Item',
    'Are you sure?',
    () => {
        // Handle confirmation
        console.log('Item deleted');
    }
);
```

### Theme Customization

Edit `assets/css/theme-constants.css` to customize colors, fonts, and spacing:

```css
:root {
    --primary: #7E57C2;        /* Primary brand color */
    --secondary: #26A69A;      /* Secondary color */
    --accent: #FFCA28;         /* Accent color */
    --success: #4CAF50;        /* Success color */
    --error: #F44336;          /* Error color */
    /* ... more variables */
}
```

## Responsive Breakpoints

- **Desktop**: > 1100px (Full sidebar visible)
- **Tablet**: 768px - 1100px (Collapsible sidebar)
- **Mobile**: < 768px (Mobile-optimized layout)
- **Small Mobile**: < 480px (Compact layout)

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Key Components

### Notification System
- **Location**: Top-right corner (desktop), full-width (mobile)
- **Types**: success, error, warning, info
- **Features**: Auto-dismiss, progress bars, action buttons, close buttons

### Modal System
- **Backdrop**: Blur effect with click-to-close
- **Animations**: Smooth slide-in/out animations
- **Forms**: Built-in form handling and validation
- **Responsive**: Adapts to screen size

### Theme System
- **Toggle**: Persistent theme switching
- **Variables**: CSS custom properties for consistency
- **Modes**: Dark (default) and Light themes

## Customization

### Adding New Pages
1. Copy an existing HTML file (e.g., `projects.html`)
2. Update the page title and active menu item
3. Include all required CSS and JS files
4. Customize the content area

### Adding New Modals
1. Add modal HTML structure to your page
2. Create trigger buttons with appropriate IDs
3. Handle form submissions in JavaScript
4. Use the modal system API to open/close

### Styling Components
- Use theme constants from `theme-constants.css`
- Follow the existing naming conventions
- Test responsiveness across all breakpoints

## Demo Features

The admin panel includes several demo features:

1. **Notification Buttons**: Test different notification types
2. **Modal Forms**: Create projects and support tickets
3. **Delete Confirmations**: Safe delete operations with confirmations
4. **Theme Toggle**: Switch between dark and light themes
5. **Responsive Tables**: Tables that adapt to mobile screens
6. **Settings Toggles**: Interactive toggle switches

## Performance

- **CSS**: Optimized with custom properties and minimal specificity
- **JavaScript**: Modular design with event delegation
- **Images**: Font icons used for scalability
- **Animations**: Hardware-accelerated transforms and opacity

## Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Focus Management**: Proper focus handling in modals
- **ARIA Labels**: Screen reader friendly
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user motion preferences

---

**Built with modern web technologies for a professional admin experience.**
